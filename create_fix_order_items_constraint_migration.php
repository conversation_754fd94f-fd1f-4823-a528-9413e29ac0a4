<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop the existing problematic constraint
        DB::statement('ALTER TABLE order_items DROP CONSTRAINT IF EXISTS order_items_status_check');
        
        // Create a new constraint with all valid status values from OrderItemStatus enum
        DB::statement("
            ALTER TABLE order_items ADD CONSTRAINT order_items_status_check 
            CHECK (status IN (
                'PENDING',
                'PLACEMENT_FAILED', 
                'ACCEPTED',
                'BACKORDERED',
                'SHIPPED',
                'REJECTED',
                'RETURNED',
                'DELIVERED',
                'CANCELLED',
                'PROCESSING',
                'PARTIALLY_SHIPPED',
                'PARTIALLY_DELIVERED'
            ))
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the constraint we created
        DB::statement('ALTER TABLE order_items DROP CONSTRAINT IF EXISTS order_items_status_check');
    }
};
