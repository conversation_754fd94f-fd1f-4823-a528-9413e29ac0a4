<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop the existing problematic constraint
        DB::statement('ALTER TABLE order_items DROP CONSTRAINT IF EXISTS order_items_status_check');

        // Check what status values currently exist in the table
        $existingStatuses = DB::table('order_items')
            ->select('status')
            ->distinct()
            ->whereNotNull('status')
            ->pluck('status')
            ->toArray();

        echo "Existing status values in order_items table: " . implode(', ', $existingStatuses) . "\n";

        // Valid status values from OrderItemStatus enum
        $validStatuses = [
            'PENDING',
            'PLACEMENT_FAILED',
            'ACCEPTED',
            'BACKORDERED',
            'SHIPPED',
            'REJECTED',
            'RETURNED',
            'DELIVERED',
            'CANCELLED',
            'PROCESSING',
            'PARTIALLY_SHIPPED',
            'PARTIALLY_DELIVERED'
        ];

        // Find invalid statuses
        $invalidStatuses = array_diff($existingStatuses, $validStatuses);

        if (!empty($invalidStatuses)) {
            echo "Found invalid status values: " . implode(', ', $invalidStatuses) . "\n";
            echo "Updating invalid status values to 'PENDING'...\n";

            // Update invalid status values to 'PENDING'
            foreach ($invalidStatuses as $invalidStatus) {
                DB::table('order_items')
                    ->where('status', $invalidStatus)
                    ->update(['status' => 'PENDING']);
            }
        }

        // Now create the constraint with valid status values
        DB::statement("
            ALTER TABLE order_items ADD CONSTRAINT order_items_status_check
            CHECK (status IN (
                'PENDING',
                'PLACEMENT_FAILED',
                'ACCEPTED',
                'BACKORDERED',
                'SHIPPED',
                'REJECTED',
                'RETURNED',
                'DELIVERED',
                'CANCELLED',
                'PROCESSING',
                'PARTIALLY_SHIPPED',
                'PARTIALLY_DELIVERED'
            ))
        ");

        echo "Successfully added order_items_status_check constraint.\n";
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the constraint we created
        DB::statement('ALTER TABLE order_items DROP CONSTRAINT IF EXISTS order_items_status_check');
    }
};
