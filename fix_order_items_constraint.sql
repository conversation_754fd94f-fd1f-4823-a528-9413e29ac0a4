-- Fix order_items status check constraint
-- This script removes the problematic check constraint and creates a new one with the correct values

-- First, drop the existing constraint
ALTER TABLE order_items DROP CONSTRAINT IF EXISTS order_items_status_check;

-- Create a new constraint that includes all valid status values from the OrderItemStatus enum
ALTER TABLE order_items ADD CONSTRAINT order_items_status_check 
CHECK (status IN (
    'PENDING',
    'PLACEMENT_FAILED', 
    'ACCEPTED',
    'BACKORDERED',
    'SHIPPED',
    'REJECTED',
    'RETURNED',
    'DELIVERED',
    'CANCELLED',
    'PROCESSING',
    'PARTIALLY_SHIPPED',
    'PARTIALLY_DELIVERED'
));
