-- Fix order_items status check constraint
-- This script removes the problematic check constraint and creates a new one with the correct values

-- First, check what status values currently exist
SELECT DISTINCT status, COUNT(*) as count
FROM order_items
WHERE status IS NOT NULL
GROUP BY status
ORDER BY status;

-- Drop the existing constraint
ALTER TABLE order_items DROP CONSTRAINT IF EXISTS order_items_status_check;

-- Update any invalid status values to 'PENDING' (you may need to adjust this based on the results above)
-- Uncomment and modify the following lines based on what invalid statuses you find:
-- UPDATE order_items SET status = 'PENDING' WHERE status = 'SOME_INVALID_STATUS';

-- Create a new constraint that includes all valid status values from the OrderItemStatus enum
ALTER TABLE order_items ADD CONSTRAINT order_items_status_check
CHECK (status IN (
    'PENDING',
    'PLACEMENT_FAILED',
    'ACCEPTED',
    'BACKORDERED',
    'SHIPPED',
    'REJECTED',
    'RETURNED',
    'DELIVERED',
    'CANCELLED',
    'PROCESSING',
    'PARTIALLY_SHIPPED',
    'PARTIALLY_DELIVERED'
));
