#!/bin/bash
set -e

# Change to the script's directory
cd "$(dirname "$0")"

GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${BLUE}[WARNING]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    if ! command -v docker >/dev/null 2>&1; then
        echo "Error: Docker is not installed"
        exit 1
    fi
    
    if ! docker info >/dev/null 2>&1; then
        echo "Error: Docker is not running"
        exit 1
    fi
}

# Start services
start_services() {
    print_status "Stopping existing services..."
    docker-compose down
    
    # Copy environment file if it doesn't exist
    if [ ! -f ".env" ] && [ -f "env.example" ]; then
        print_status "Creating .env file from template..."
        cp env.example .env
    fi
    
    print_status "Starting Docker services..."
    docker-compose up -d --build
}

# Wait for services
wait_for_services() {
    print_status "Waiting for services..."
    
    # Wait for database
    timeout=30
    while ! docker-compose exec -T database pg_isready -U highfive >/dev/null 2>&1; do
        sleep 2
        timeout=$((timeout - 2))
        [ $timeout -le 0 ] && break
    done
    
    # Wait for app
    timeout=30
    while ! curl -sf http://admin.highfive.local/up >/dev/null 2>&1; do
        sleep 2
        timeout=$((timeout - 2))
        [ $timeout -le 0 ] && break
    done
}

# Install dependencies
install_dependencies() {
    print_status "Installing PHP dependencies..."
    
    # Install dependencies (auth.json is now mounted as volume)
    docker-compose exec -T app composer install --optimize-autoloader --no-interaction
    
    # Set permissions only for directories that need it (avoid .git and vendor)
    # docker-compose exec -T app chmod -R 755 storage bootstrap/cache 2>/dev/null || true
}

# Setup Laravel
setup_laravel() {
    print_status "Setting up Laravel..."
    
    # Generate key if needed
    if ! grep -q "APP_KEY=base64:" ".env"; then
        docker-compose exec -T app php artisan key:generate --force
    fi
    
    # Run migrations
    docker-compose exec -T app php artisan migrate --force
    
    # Basic seeding
    docker-compose exec -T app php artisan db:seed --force 2>/dev/null || true

    # Install demo data
    docker-compose exec -T app php artisan demo:install --force 2>/dev/null || true
}

# Main function
main() {
    echo "��️  HighFive Setup"
    
    check_prerequisites
    start_services
    wait_for_services
    install_dependencies
    setup_laravel
    
    print_success "Setup complete!"
    echo ""
    echo "==================== Service URLs and Credentials ===================="
    echo "🌐 Application:           http://highfive.local"
    echo "🔑 Laravel Nova Admin:    http://admin.highfive.local/nova"
    echo "    • Login: <EMAIL> / password"
    echo "📊 Laravel Horizon:       http://admin.highfive.local/horizon"
    echo "🔍 OpenSearch API:        http://opensearch:9200"
    echo "📈 OpenSearch Dashboards: http://localhost:5601"
    echo "💾 PostgreSQL:            database:5432 (DB: highfive, User: highfive, Pass: secret)"
    echo "⚡ Redis:                 redis:6379"
    echo "📧 Mailpit:               http://mailpit:8025"
    echo "======================================================================="
    echo ""
}

main "$@" 